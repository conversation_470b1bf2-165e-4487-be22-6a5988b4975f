﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Database.Migrations;

/// <inheritdoc />
public partial class Add_History_Table : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "histories",
            schema: "public",
            columns: table => new
            {
                id = table.Column<Guid>(type: "uuid", nullable: false),
                table_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                property_name = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                property_type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                previous_value = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: false),
                current_value = table.Column<string>(type: "character varying(4000)", maxLength: 4000, nullable: false),
                created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                last_modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                is_deleted = table.Column<bool>(type: "boolean", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("pk_histories", x => x.id);
            });

        migrationBuilder.CreateIndex(
            name: "ix_histories_table_name_created_at",
            schema: "public",
            table: "histories",
            columns: ["table_name", "created_at"]);
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "histories",
            schema: "public");
    }
}
