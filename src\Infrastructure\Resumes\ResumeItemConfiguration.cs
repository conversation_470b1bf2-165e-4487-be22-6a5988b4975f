﻿using Domain.Resumes;
using Domain.Todos;
using Domain.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Infrastructure.Resumes;

internal sealed class ResumeConfiguration : IEntityTypeConfiguration<Resume>
{
    public void Configure(EntityTypeBuilder<Resume> builder)
    {
        builder.HasKey(t => t.Id);

        builder.HasOne<User>().WithMany().HasForeign<PERSON>ey(t => t.UserId);
        builder.HasOne<Resume>().WithMany().Has<PERSON>oreign<PERSON>ey(t => t.ParentId);
    }
}
