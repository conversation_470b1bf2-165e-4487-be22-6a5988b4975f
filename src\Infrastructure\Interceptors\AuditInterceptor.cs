using Domain;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Diagnostics;
using SharedKernel;

namespace Infrastructure.Interceptors;

public sealed class AuditInterceptor(IDateTimeProvider dateTimeProvider) : SaveChangesInterceptor
{
    private readonly List<Domain.History.History> _auditEntries = [];

    public override InterceptionResult<int> SavingChanges(DbContextEventData eventData, InterceptionResult<int> result)
    {
        CreateAuditEntries(eventData.Context);
        return base.SavingChanges(eventData, result);
    }

    public override ValueTask<InterceptionResult<int>> SavingChangesAsync(
        DbContextEventData eventData, 
        InterceptionResult<int> result, 
        CancellationToken cancellationToken = default)
    {
        CreateAuditEntries(eventData.Context);
        return base.SavingChangesAsync(eventData, result, cancellationToken);
    }

    public override int SavedChanges(SaveChangesCompletedEventData eventData, int result)
    {
        SaveAuditEntries(eventData.Context);
        return base.SavedChanges(eventData, result);
    }

    public override async ValueTask<int> SavedChangesAsync(
        SaveChangesCompletedEventData eventData, 
        int result, 
        CancellationToken cancellationToken = default)
    {
        await SaveAuditEntriesAsync(eventData.Context, cancellationToken);
        return await base.SavedChangesAsync(eventData, result, cancellationToken);
    }

    private void CreateAuditEntries(DbContext? context)
    {
        if (context == null)
        {
            return;
        }

        _auditEntries.Clear();

        foreach (EntityEntry<BaseEntity> entry in context.ChangeTracker.Entries<BaseEntity>())
        {
            // Only track Modified entities for now (you can extend this for Added/Deleted)
            if (entry.State == EntityState.Modified)
            {
                CreateAuditEntriesForModifiedEntity(entry);
            }
        }
    }

    private void CreateAuditEntriesForModifiedEntity(EntityEntry<BaseEntity> entry)
    {
        string tableName = entry.Metadata.GetTableName() ?? entry.Entity.GetType().Name;

        foreach (PropertyEntry property in entry.Properties)
        {
            // Skip properties that haven't changed
            if (!property.IsModified)
            {
                continue;
            }

            // Skip audit properties to avoid noise
            if (IsAuditProperty(property.Metadata.Name))
            {
                continue;
            }

            string? previousValue = property.OriginalValue?.ToString();
            string? currentValue = property.CurrentValue?.ToString();

            // Only create history entry if values actually changed
            if (previousValue != currentValue)
            {
                Domain.History.History historyEntry = new()
                {
                    TableName = tableName,
                    PropertyName = property.Metadata.Name,
                    PropertyType = property.Metadata.ClrType.Name,
                    PreviousValue = previousValue ?? string.Empty,
                    CurrentValue = currentValue ?? string.Empty,
                    CreatedAt = dateTimeProvider.UtcNow
                };

                _auditEntries.Add(historyEntry);
            }
        }
    }

    private void SaveAuditEntries(DbContext? context)
    {
        if (context == null || !_auditEntries.Any())
        {
            return;
        }

        context.Set<Domain.History.History>().AddRange(_auditEntries);
        context.SaveChanges();
        _auditEntries.Clear();
    }

    private async Task SaveAuditEntriesAsync(DbContext? context, CancellationToken cancellationToken)
    {
        if (context == null || !_auditEntries.Any())
        {
            return;
        }

        context.Set<Domain.History.History>().AddRange(_auditEntries);
        await context.SaveChangesAsync(cancellationToken);
        _auditEntries.Clear();
    }

    private static bool IsAuditProperty(string propertyName)
    {
        return propertyName is nameof(BaseEntity.CreatedAt) 
            or nameof(BaseEntity.LastModifiedAt) 
            or nameof(BaseEntity.IsDeleted);
    }
}
