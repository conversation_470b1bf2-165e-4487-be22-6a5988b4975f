﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Infrastructure.Database.Migrations;

/// <inheritdoc />
public partial class Add_Jobs_And_Resumes_Tables : Migration
{
    /// <inheritdoc />
    protected override void Up(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.CreateTable(
            name: "jobs",
            schema: "public",
            columns: table => new
            {
                id = table.Column<Guid>(type: "uuid", nullable: false),
                user_id = table.Column<Guid>(type: "uuid", nullable: false),
                job_title = table.Column<string>(type: "text", nullable: false),
                job_description = table.Column<string>(type: "text", nullable: false),
                job_posting_url = table.Column<string>(type: "text", nullable: false),
                company_url = table.Column<string>(type: "text", nullable: false),
                applied_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                last_modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                is_deleted = table.Column<bool>(type: "boolean", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("pk_jobs", x => x.id);
                table.ForeignKey(
                    name: "fk_jobs_users_user_id",
                    column: x => x.user_id,
                    principalSchema: "public",
                    principalTable: "users",
                    principalColumn: "id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateTable(
            name: "resumes",
            schema: "public",
            columns: table => new
            {
                id = table.Column<Guid>(type: "uuid", nullable: false),
                user_id = table.Column<Guid>(type: "uuid", nullable: false),
                parent_id = table.Column<Guid>(type: "uuid", nullable: false),
                resume_content = table.Column<string>(type: "text", nullable: false),
                created_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                last_modified_at = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                is_deleted = table.Column<bool>(type: "boolean", nullable: false)
            },
            constraints: table =>
            {
                table.PrimaryKey("pk_resumes", x => x.id);
                table.ForeignKey(
                    name: "fk_resumes_resumes_parent_id",
                    column: x => x.parent_id,
                    principalSchema: "public",
                    principalTable: "resumes",
                    principalColumn: "id",
                    onDelete: ReferentialAction.Cascade);
                table.ForeignKey(
                    name: "fk_resumes_users_user_id",
                    column: x => x.user_id,
                    principalSchema: "public",
                    principalTable: "users",
                    principalColumn: "id",
                    onDelete: ReferentialAction.Cascade);
            });

        migrationBuilder.CreateIndex(
            name: "ix_jobs_user_id",
            schema: "public",
            table: "jobs",
            column: "user_id");

        migrationBuilder.CreateIndex(
            name: "ix_resumes_parent_id",
            schema: "public",
            table: "resumes",
            column: "parent_id");

        migrationBuilder.CreateIndex(
            name: "ix_resumes_user_id",
            schema: "public",
            table: "resumes",
            column: "user_id");
    }

    /// <inheritdoc />
    protected override void Down(MigrationBuilder migrationBuilder)
    {
        migrationBuilder.DropTable(
            name: "jobs",
            schema: "public");

        migrationBuilder.DropTable(
            name: "resumes",
            schema: "public");
    }
}
